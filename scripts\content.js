// --- Gemini Translator Content Script (Refactored & Optimized) ---
let preloadedCache = false;
let pageTranslated = false;
let currentUrl = window.location.href;
let currentDomain = extractDomain(currentUrl);
let lastCheckedUrl = currentUrl;
let urlChangeInterval = null;
let contentScriptFullyInitialized = false;

console.log("Content script loaded on page:", currentUrl);
console.log("Current domain:", currentDomain);

// Mark content script as loaded (but not fully initialized yet)
window.geminiTranslatorLoaded = false;
window.geminiTranslatorInitializing = true;
console.log("Gemini Translator content script loading...");

// --- Utility Functions ---
function extractDomain(url) {
  try {
    return new URL(url).hostname;
  } catch (e) {
    console.error("Error extracting domain:", e);
    return "";
  }
}

function preloadCache() {
  if (preloadedCache) return;
  initializeCache().then(() => {
    preloadedCache = true;
    console.log("Translation cache preloaded for faster performance");
  });
}

// --- Auto Translation Logic ---
function checkForAutoTranslation() {
  console.log("Checking for auto-translation on domain:", currentDomain);
  pageTranslated = false;
  // Ultra-fast initial delay for maximum startup speed
  setTimeout(() => {
    const textNodes = document.body.querySelectorAll(
      "p, h1, h2, h3, h4, h5, h6, span, div, a, button, li"
    );
    if (textNodes.length < 5) {
      console.log(
        "Not enough content to translate yet, will retry in 200ms"
      );
      // Immediate retry for maximum responsiveness
      setTimeout(checkForAutoTranslation, 50);
      return;
    }
    browser.storage.local
      .get(["domainTranslationSettings", "persistentTranslationEnabled"])
      .then((result) => {
        if (
          !(
            result.persistentTranslationEnabled &&
            result.domainTranslationSettings
          )
        )
          return;
        try {
          const domainSettings = JSON.parse(result.domainTranslationSettings);
          const settings = domainSettings[currentDomain];
          if (!settings)
            return console.log(
              "No translation settings found for domain:",
              currentDomain
            );
          translatePage(
            settings.targetLanguage,
            settings.apiKey,
            settings.model,
            settings.tone
          )
            .then(() => {
              console.log("Auto-translation completed successfully");
              pageTranslated = true;
            })
            .catch((error) => {
              console.error("Auto-translation failed:", error);
              pageTranslated = false;
              // Ultra-fast retry delay for maximum speed
              setTimeout(checkForAutoTranslation, 300);
            });
        } catch (error) {
          console.error("Error parsing domain settings:", error);
        }
      })
      .catch((error) =>
        console.error("Error checking for auto-translation:", error)
      );
  }, 100); // Reduced from 500ms to 100ms for faster startup
}

// --- SPA/URL Change Detection ---
function startUrlChangeDetection() {
  if (urlChangeInterval) clearInterval(urlChangeInterval);
  const observer = new MutationObserver((mutations) => {
    let significantChanges = mutations.some(
      (m) => m.addedNodes.length > 5 || m.removedNodes.length > 5
    );
    if (!significantChanges) return;
    const newUrl = window.location.href;
    if (newUrl === lastCheckedUrl || extractDomain(newUrl) !== currentDomain)
      return;
    currentUrl = lastCheckedUrl = newUrl;
    pageTranslated = false;
    setTimeout(() => {
      console.log(
        "Checking for auto-translation after significant DOM changes"
      );
      checkForAutoTranslation();
    }, 1000);
  });
  observer.observe(document.body, { childList: true, subtree: true });
  urlChangeInterval = setInterval(() => {
    const newUrl = window.location.href;
    if (newUrl === lastCheckedUrl) return;
    currentUrl = lastCheckedUrl = newUrl;
    pageTranslated = false;
    setTimeout(() => {
      console.log("Checking for auto-translation after URL change");
      checkForAutoTranslation();
    }, 1000);
  }, 1000);
}

function setupHistoryChangeDetection() {
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;
  function handleHistoryChange() {
    const newUrl = window.location.href;
    if (newUrl === lastCheckedUrl) return;
    currentUrl = lastCheckedUrl = newUrl;
    pageTranslated = false;
    setTimeout(() => {
      console.log("Checking for auto-translation after history event");
      checkForAutoTranslation();
    }, 1000);
  }
  history.pushState = function () {
    originalPushState.apply(this, arguments);
    handleHistoryChange();
  };
  history.replaceState = function () {
    originalReplaceState.apply(this, arguments);
    handleHistoryChange();
  };
  window.addEventListener("popstate", handleHistoryChange);
}

// --- Init ---
preloadCache();
setTimeout(() => {
  checkForAutoTranslation();
  startUrlChangeDetection();
  setupHistoryChangeDetection();
}, 100); // Ultra-fast initialization for maximum speed

// Function to detect URL changes and DOM mutations for SPA (Single Page Applications)
function startUrlChangeDetection() {
  // Clear any existing interval
  if (urlChangeInterval) {
    clearInterval(urlChangeInterval);
  }

  // Set up a MutationObserver to detect DOM changes that might indicate page navigation in SPAs
  const observer = new MutationObserver((mutations) => {
    // Check if significant DOM changes occurred (potential page navigation in SPA)
    let significantChanges = false;

    for (const mutation of mutations) {
      // Check for added nodes that might indicate page content changes
      if (mutation.addedNodes.length > 5) {
        significantChanges = true;
        break;
      }

      // Check for removed nodes that might indicate page content changes
      if (mutation.removedNodes.length > 5) {
        significantChanges = true;
        break;
      }
    }

    if (significantChanges) {
      console.log("Significant DOM changes detected - possible SPA navigation");

      // Only reset and check if we're on the same domain and URL has changed
      const newUrl = window.location.href;
      if (
        newUrl !== lastCheckedUrl &&
        extractDomain(newUrl) === currentDomain
      ) {
        // Update current URL and last checked URL
        currentUrl = newUrl;
        lastCheckedUrl = newUrl;

        // Reset translation state for the new content
        pageTranslated = false;

        // Force a check for auto-translation with ultra-fast delay
        setTimeout(() => {
          console.log(
            "Checking for auto-translation after significant DOM changes"
          );
          checkForAutoTranslation();
        }, 200); // Ultra-fast response to DOM changes
      }
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false,
  });

  // Also check for URL changes every 1000ms (1 second) as a fallback
  urlChangeInterval = setInterval(() => {
    const newUrl = window.location.href;

    // If URL has changed but domain is the same
    if (newUrl !== lastCheckedUrl) {
      console.log("URL changed from", lastCheckedUrl, "to", newUrl);

      // Update current URL and last checked URL
      currentUrl = newUrl;
      lastCheckedUrl = newUrl;

      // Reset translation state for the new page
      pageTranslated = false;

      // Force a check for auto-translation with ultra-fast delay
      setTimeout(() => {
        console.log("Checking for auto-translation after URL change");
        checkForAutoTranslation();
      }, 200); // Ultra-fast response to URL changes
    }
  }, 500); // Faster URL change detection
}

// Preload cache when the page loads
preloadCache();

// Monitor history state changes (used by many SPAs for navigation)
function setupHistoryChangeDetection() {
  // Store original history methods
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  // Override pushState
  history.pushState = function () {
    // Call the original function first
    originalPushState.apply(this, arguments);

    // Then handle the URL change
    const newUrl = window.location.href;
    if (newUrl !== lastCheckedUrl) {
      console.log("History pushState detected, URL changed to:", newUrl);

      // Update URLs
      currentUrl = newUrl;
      lastCheckedUrl = newUrl;

      // Reset translation state
      pageTranslated = false;

      // Check for auto-translation with a delay to let the page render
      setTimeout(() => {
        console.log("Checking for auto-translation after history pushState");
        checkForAutoTranslation();
      }, 1000);
    }
  };

  // Override replaceState
  history.replaceState = function () {
    // Call the original function first
    originalReplaceState.apply(this, arguments);

    // Then handle the URL change
    const newUrl = window.location.href;
    if (newUrl !== lastCheckedUrl) {
      console.log("History replaceState detected, URL changed to:", newUrl);

      // Update URLs
      currentUrl = newUrl;
      lastCheckedUrl = newUrl;

      // Reset translation state
      pageTranslated = false;

      // Check for auto-translation with a delay to let the page render
      setTimeout(() => {
        console.log("Checking for auto-translation after history replaceState");
        checkForAutoTranslation();
      }, 1000);
    }
  };

  // Listen for popstate events (browser back/forward buttons)
  window.addEventListener("popstate", function () {
    const newUrl = window.location.href;
    if (newUrl !== lastCheckedUrl) {
      console.log("Popstate event detected, URL changed to:", newUrl);

      // Update URLs
      currentUrl = newUrl;
      lastCheckedUrl = newUrl;

      // Reset translation state
      pageTranslated = false;

      // Check for auto-translation with ultra-fast delay
      setTimeout(() => {
        console.log("Checking for auto-translation after popstate event");
        checkForAutoTranslation();
      }, 200); // Ultra-fast response to history changes
    }
  });
}

// Check for auto-translation with ultra-fast delay for maximum speed
setTimeout(() => {
  checkForAutoTranslation();

  // Start monitoring for URL and DOM changes (for SPAs)
  startUrlChangeDetection();

  // Set up history API monitoring
  setupHistoryChangeDetection();

  // Mark content script as fully initialized
  window.geminiTranslatorLoaded = true;
  window.geminiTranslatorInitializing = false;
  console.log("Gemini Translator content script fully initialized");
}, 100); // Ultra-fast initialization for maximum speed

// Listen for messages from the popup or background script
browser.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  console.log("Content script received message:", message.action);

  // Add error handling wrapper for all message responses
  const safeResponse = (responseData) => {
    try {
      sendResponse(responseData);
    } catch (error) {
      console.error("Error sending response:", error);
      // Try to send a basic error response
      try {
        sendResponse({ success: false, error: "Response sending failed" });
      } catch (fallbackError) {
        console.error("Failed to send fallback response:", fallbackError);
      }
    }
  };

  // Check if this is a YouTube subtitle message
  const youtubeActions = ["loood", "testSubtitleDisplay", "updateYouTubeSubtitlesSettings", "updateSubtitleCustomization"];
  if (youtubeActions.includes(message.action)) {
    return handleYouTubeSubtitleMessage(message, safeResponse);
  }

  if (message.action === "ping") {
    // Enhanced ping response to check if content script is fully loaded
    const isInitialized = window.geminiTranslatorLoaded === true;
    const isInitializing = window.geminiTranslatorInitializing === true;

    safeResponse({
      success: true,
      status: isInitialized ? "ready" : (isInitializing ? "initializing" : "loading"),
      initialized: isInitialized,
      initializing: isInitializing,
      timestamp: Date.now(),
      url: window.location.href,
      domain: extractDomain(window.location.href)
    });
    return true;
  } else if (message.action === "translate") {
    // If the page is already translated, don't translate again
    if (pageTranslated) {
      console.log("Page already translated, skipping");
      safeResponse({ success: true, alreadyTranslated: true });
      return true;
    }

    console.log(
      "Starting translation with target language:",
      message.targetLanguage
    );

    // Ensure cache is loaded
    if (!preloadedCache) {
      preloadCache();
    }

    // Update the current URL and domain
    currentUrl = window.location.href;
    currentDomain = extractDomain(currentUrl);
    console.log("Updated current domain:", currentDomain);

    translatePage(
      message.targetLanguage,
      message.apiKey,
      message.model,
      message.tone
    )
      .then(() => {
        console.log("Translation completed successfully");

        // Mark this page as translated
        pageTranslated = true;

        // Notify the background script about the successful translation
        browser.runtime
          .sendMessage({
            action: "updateTranslationParams",
            targetLanguage: message.targetLanguage,
            apiKey: message.apiKey,
            model: message.model,
            tone: message.tone,
            domain: currentDomain,
          })
          .catch((error) => {
            console.error("Error updating translation parameters:", error);
          });

        safeResponse({ success: true });
      })
      .catch((error) => {
        console.error("Translation failed:", error);
        safeResponse({ success: false, error: error.message });
      });
    return true; // Required for async sendResponse
  } else if (message.action === "checkDomainTranslation") {
    // Check if we should translate this page based on domain settings
    checkForAutoTranslation();
    safeResponse({ success: true });
    return true;
  } else if (message.action === "askQuestion") {
    console.log(
      "Received askQuestion action with model:",
      message.model,
      "and language:",
      message.responseLang
    );
    // Get page content to provide context for page-related questions
    const pageContent = getPageContent();
    console.log("Page content extracted, title:", pageContent.title);
    // Call askGemini which can now handle both page-specific and general questions
    askGemini(
      pageContent,
      message.question,
      message.apiKey,
      message.model,
      message.responseLang,
      message.tone
    )
      .then((answer) => {
        console.log("Got answer from Gemini API");
        safeResponse({ success: true, answer: answer });
      })
      .catch((error) => {
        console.error("Error in askQuestion:", error);
        safeResponse({ success: false, error: error.message });
      });
    return true; // Required for async sendResponse
  } else if (message.action === "translateText") {
    const targetLang = message.targetLanguage;
    const sourceLang = message.sourceLanguage || "auto";
    const prompt = `Translate the following text from ${getLanguageName(
      sourceLang
    )} to ${getLanguageName(
      targetLang
    )}. Return ONLY the translated text without any explanations or additional information:\n\n"${
      message.sourceText
    }"`;
    callGeminiAPI(prompt, message.apiKey, message.model)
      .then((translatedText) => {
        // Update popup with translation
        safeResponse({
          success: true,
          translatedText: cleanTranslatedText(translatedText, targetLang),
        });
        isTranslating = false;
      })
      .catch((error) => {
        console.error("Translation error:", error);
        safeResponse({
          success: false,
          error: error.message || error.toString(),
        });
        isTranslating = false;
      });
    return true; // Required for async sendResponse
  }
});

// Function to get the content of the page with ultra-optimized performance
async function getPageContent() {
  // Get the title of the page
  const title = document.title;

  // Get meta description if available
  let metaDescription = "";
  const metaDescElement = document.querySelector('meta[name="description"]');
  if (metaDescElement) {
    metaDescription = metaDescElement.getAttribute("content");
  }

  // Get all text nodes and their parent elements with optimized filtering
  const textNodes = [];

  // Skip these tags completely for better performance
  const skipTags = new Set([
    "SCRIPT",
    "STYLE",
    "NOSCRIPT",
    "META",
    "LINK",
    "SVG",
    "PATH",
    "IFRAME",
  ]);

  // Use a more efficient TreeWalker configuration
  const walker = document.createTreeWalker(
    document.body,
    NodeFilter.SHOW_TEXT,
    {
      acceptNode: function (node) {
        // Skip empty text nodes or nodes with only whitespace (faster check)
        if (!node.nodeValue || !node.nodeValue.trim()) {
          return NodeFilter.FILTER_REJECT;
        }

        // Skip nodes in hidden elements
        const parent = node.parentNode;
        if (!parent || skipTags.has(parent.tagName)) {
          return NodeFilter.FILTER_REJECT;
        }

        // Skip elements that are not visible
        const style = window.getComputedStyle(parent);
        if (style.display === "none" || style.visibility === "hidden") {
          return NodeFilter.FILTER_REJECT;
        }

        return NodeFilter.FILTER_ACCEPT;
      },
    }
  );

  // Ultra-optimized batch processing for maximum performance
  let node;
  const batchSize = 1000; // Process nodes in larger batches
  let batch = [];

  while ((node = walker.nextNode())) {
    const trimmedText = node.nodeValue.trim();
    if (trimmedText.length > 0) { // Only process non-empty text nodes
      batch.push({
        node: node,
        text: trimmedText,
        parentElement: node.parentNode,
      });

      // Process in batches to avoid blocking the main thread
      if (batch.length >= batchSize) {
        textNodes.push(...batch);
        batch = [];
        // Allow other tasks to run
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }
  }

  // Add remaining nodes
  if (batch.length > 0) {
    textNodes.push(...batch);
  }

  // Combine all the content for the API request
  const bodyText = textNodes.map((item) => item.text).join("\n");

  return {
    title: title,
    metaDescription: metaDescription,
    content: bodyText,
    textNodes: textNodes,
  };
}

// Function to add RTL support to the page for Persian/Arabic content
function addRTLSupport(targetLanguage) {
  if (targetLanguage === "fa" || targetLanguage === "ar") {
    // Add a style element to the head with RTL support
    const styleElement = document.createElement("style");
    styleElement.id = "gemini-translator-rtl-style";
    styleElement.textContent = `
      /* RTL support for Persian/Arabic text */
      [data-gemini-translated="true"] {
        font-family: 'Tahoma', 'Arial', sans-serif !important;
        direction: rtl !important;
        text-align: right !important;
        unicode-bidi: embed !important;
      }

      /* Fix for mixed content (English in Persian text) */
      [data-gemini-translated="true"] span.gemini-ltr {
        direction: ltr !important;
        display: inline-block !important;
        unicode-bidi: embed !important;
      }
    `;
    document.head.appendChild(styleElement);

    // Add RTL direction to the html or body element
    document.documentElement.setAttribute("dir", "rtl");
  }
}

// Function to remove RTL support when restoring original content
function removeRTLSupport() {
  // Remove the style element
  const styleElement = document.getElementById("gemini-translator-rtl-style");
  if (styleElement) {
    styleElement.remove();
  }

  // Remove RTL direction from the html element
  document.documentElement.removeAttribute("dir");

  // Remove data attributes from all elements
  const translatedElements = document.querySelectorAll(
    '[data-gemini-translated="true"]'
  );
  translatedElements.forEach((element) => {
    element.removeAttribute("data-gemini-translated");
  });
}

// Enhanced translation cache with persistent storage and optimized performance
const translationCache = new Map();
let cacheInitialized = false;
let cacheInitPromise = null;

// Initialize cache from browser storage (optimized with promise caching)
async function initializeCache() {
  if (cacheInitialized) return;
  if (cacheInitPromise) return cacheInitPromise;

  cacheInitPromise = (async () => {
    try {
      const result = await browser.storage.local.get("translationCache");
      if (result.translationCache) {
        const cachedEntries = JSON.parse(result.translationCache);
        // Use batch operations for better performance
        Object.entries(cachedEntries).forEach(([key, value]) => {
          translationCache.set(key, value);
        });
      }
      cacheInitialized = true;
    } catch (error) {
      console.error("Error initializing translation cache:", error);
    }
  })();

  return cacheInitPromise;
}

// Function to get cached translation or null if not found (optimized)
function getCachedTranslation(text, targetLanguage, model) {
  if (!cacheInitialized) return null;
  const cacheKey = `${text}|${targetLanguage}|${model}`;
  return translationCache.get(cacheKey);
}

// Function to get multiple cached translations at once (batch operation)
function getBatchCachedTranslations(items, targetLanguage, model) {
  if (!cacheInitialized) return new Map();

  const results = new Map();
  items.forEach(item => {
    const cacheKey = `${item.text}|${targetLanguage}|${model}`;
    const cached = translationCache.get(cacheKey);
    if (cached) {
      results.set(item.id, cached);
    }
  });
  return results;
}

// Function to add translation to cache (ultra-optimized)
function cacheTranslation(text, translation, targetLanguage, model) {
  const cacheKey = `${text}|${targetLanguage}|${model}`;
  translationCache.set(cacheKey, translation);

  // Ultra-fast debounced save to storage to avoid excessive writes
  if (!cacheTranslation.saveTimeout) {
    cacheTranslation.saveTimeout = setTimeout(() => {
      const cacheObject = Object.fromEntries(translationCache);
      browser.storage.local.set({
        translationCache: JSON.stringify(cacheObject),
      });
      cacheTranslation.saveTimeout = null;
    }, 100); // Reduced to 100ms for even faster persistence
  }
}

// Batch cache multiple translations at once (ultra-optimized)
function batchCacheTranslations(translations, targetLanguage, model) {
  // Use batch operations for maximum performance
  const batchEntries = translations.map(({ text, translation }) => [
    `${text}|${targetLanguage}|${model}`,
    translation
  ]);

  // Batch set all entries at once
  batchEntries.forEach(([key, value]) => {
    translationCache.set(key, value);
  });

  // Ultra-fast debounced save with same logic
  if (!cacheTranslation.saveTimeout) {
    cacheTranslation.saveTimeout = setTimeout(() => {
      const cacheObject = Object.fromEntries(translationCache);
      browser.storage.local.set({
        translationCache: JSON.stringify(cacheObject),
      });
      cacheTranslation.saveTimeout = null;
    }, 100); // Reduced to 100ms for even faster persistence
  }
}

// Function to translate the page using Gemini API
async function translatePage(
  targetLanguage,
  apiKey,
  model = "gemini-1.5-flash",
  tone = "neutral"
) {
  const pageContent = await getPageContent();

  // Save original text nodes for restoration
  const originalTextNodes = pageContent.textNodes.map((item) => ({
    node: item.node,
    text: item.text,
  }));

  // Save original title
  const originalTitle = document.title;

  // Store original content for restoration
  browser.storage.local.set({
    originalTextNodes: JSON.stringify(
      originalTextNodes.map((item) => item.text)
    ),
    originalTitle: originalTitle,
  });

  // Store domain-specific translation settings for persistent translation
  storeDomainTranslationSettings(targetLanguage, apiKey, model, tone);

  // Add RTL support for Persian/Arabic
  if (targetLanguage === "fa" || targetLanguage === "ar") {
    addRTLSupport(targetLanguage);
  }

  // Function to store domain-specific translation settings
  async function storeDomainTranslationSettings(
    targetLanguage,
    apiKey,
    model,
    tone
  ) {
    try {
      // Get current domain settings
      const result = await browser.storage.local.get([
        "domainTranslationSettings",
      ]);
      let domainSettings = {};

      if (result.domainTranslationSettings) {
        domainSettings = JSON.parse(result.domainTranslationSettings);
      }

      // Update settings for current domain
      domainSettings[currentDomain] = {
        targetLanguage: targetLanguage,
        apiKey: apiKey,
        model: model,
        tone: tone,
        lastTranslated: new Date().toISOString(),
      };

      // Save updated settings
      await browser.storage.local.set({
        domainTranslationSettings: JSON.stringify(domainSettings),
      });

      // Get the current persistent translation setting instead of automatically enabling it
      const persistentSettings = await browser.storage.local.get([
        "persistentTranslationEnabled",
      ]);
      console.log(
        "Current persistent translation setting:",
        persistentSettings.persistentTranslationEnabled
      );

      console.log("Domain translation settings saved for:", currentDomain);
    } catch (error) {
      console.error("Error storing domain translation settings:", error);
    }
  }

  // Initialize cache once at the beginning (optimized)
  await initializeCache();

  // First translate the title (optimized with immediate cache check)
  try {
    const cachedTitle = getCachedTranslation(
      pageContent.title,
      targetLanguage,
      model
    );
    let translatedTitle;

    if (cachedTitle) {
      translatedTitle = cachedTitle;
    } else {
      // Translate the title with optimized prompt
      let titlePrompt = `You are a professional translator. Translate to ${getLanguageName(
        targetLanguage
      )}: "${
        pageContent.title
      }". IMPORTANT: Provide ONLY the translated text without any explanations. Do not transliterate - translate the meaning.`;

      // Add tone instructions if a tone is selected
      if (tone && tone !== "neutral") {
        const toneInstructions = {
          casual: "Use a casual, friendly, and conversational tone.",
          formal: "Use a formal, professional, and polite tone.",
          simple: "Use simple, clear language that's easy to understand.",
          literary: "Use a literary, poetic, and expressive tone.",
        };

        if (toneInstructions[tone]) {
          titlePrompt += ` ${toneInstructions[tone]}`;
        }
      }

      translatedTitle = await callGeminiAPI(titlePrompt, apiKey, model);
      // Cache the translated title
      cacheTranslation(
        pageContent.title,
        translatedTitle,
        targetLanguage,
        model
      );
    }

    // Update the page title immediately
    document.title = cleanTranslatedText(translatedTitle, targetLanguage);

    // Show translation in progress indicator
    const progressIndicator = document.createElement("div");
    progressIndicator.textContent =
      targetLanguage === "fa"
        ? "در حال ترجمه..."
        : "Translation in progress...";
    progressIndicator.style.position = "fixed";
    progressIndicator.style.top = "50%";
    progressIndicator.style.left = "50%";
    progressIndicator.style.transform = "translate(-50%, -50%)";
    progressIndicator.style.padding = "20px";
    progressIndicator.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
    progressIndicator.style.color = "white";
    progressIndicator.style.borderRadius = "10px";
    progressIndicator.style.zIndex = "10000";
    progressIndicator.id = "translation-progress";

    // Add RTL support for Persian
    if (targetLanguage === "fa") {
      progressIndicator.style.fontFamily = "Tahoma, Arial, sans-serif";
      progressIndicator.dir = "rtl";
    }

    document.body.appendChild(progressIndicator);

    // Translate text in ultra-large chunks for maximum speed
    const chunkSize = 1000; // Maximum chunk size for ultimate translation speed

    // Prepare all chunks for processing with priority for visible content
    let allChunks = [];

    // Enhanced viewport detection functions for two-phase translation
    const getViewportBounds = () => {
      return {
        top: window.pageYOffset || document.documentElement.scrollTop,
        left: window.pageXOffset || document.documentElement.scrollLeft,
        bottom: (window.pageYOffset || document.documentElement.scrollTop) +
                (window.innerHeight || document.documentElement.clientHeight),
        right: (window.pageXOffset || document.documentElement.scrollLeft) +
               (window.innerWidth || document.documentElement.clientWidth),
        height: window.innerHeight || document.documentElement.clientHeight,
        width: window.innerWidth || document.documentElement.clientWidth
      };
    };

    // Check if element is currently visible in viewport
    const isInViewport = (element) => {
      if (!element || !element.getBoundingClientRect) return false;

      const rect = element.getBoundingClientRect();
      const viewport = getViewportBounds();

      // Element is visible if any part is within the current viewport
      return (
        rect.top < viewport.height &&
        rect.bottom > 0 &&
        rect.left < viewport.width &&
        rect.right > 0 &&
        rect.width > 0 &&
        rect.height > 0
      );
    };

    // Check if element is in extended viewport (for near-viewport content)
    const isInExtendedViewport = (element, threshold = 0.5) => {
      if (!element || !element.getBoundingClientRect) return false;

      const rect = element.getBoundingClientRect();
      const viewport = getViewportBounds();
      const extendedHeight = viewport.height * threshold;

      return (
        rect.top < viewport.height + extendedHeight &&
        rect.bottom > -extendedHeight &&
        rect.left < viewport.width &&
        rect.right > 0
      );
    };

    // Two-phase translation approach: Separate content by visibility priority
    const currentViewportNodes = [];
    const nearViewportNodes = [];
    const backgroundNodes = [];

    // Categorize nodes by visibility priority for two-phase translation
    pageContent.textNodes.forEach((node) => {
      if (isInViewport(node.parentElement)) {
        // Phase 1: Currently visible content (highest priority)
        currentViewportNodes.push(node);
      } else if (isInExtendedViewport(node.parentElement, 0.5)) {
        // Phase 1: Near-viewport content (medium priority)
        nearViewportNodes.push(node);
      } else {
        // Phase 2: Background content (lowest priority)
        backgroundNodes.push(node);
      }
    });

    // Phase 1 chunks: Visible and near-viewport content
    const phase1Chunks = [];
    const phase2Chunks = [];

    // Process current viewport content first (immediate visibility)
    for (let i = 0; i < currentViewportNodes.length; i += chunkSize) {
      phase1Chunks.push(currentViewportNodes.slice(i, i + chunkSize));
    }

    // Then process near-viewport content (likely to be seen soon)
    for (let i = 0; i < nearViewportNodes.length; i += chunkSize) {
      phase1Chunks.push(nearViewportNodes.slice(i, i + chunkSize));
    }

    // Phase 2 chunks: Background content (translate after visible content)
    for (let i = 0; i < backgroundNodes.length; i += chunkSize) {
      phase2Chunks.push(backgroundNodes.slice(i, i + chunkSize));
    }

    // Combine all chunks with phase 1 first
    const chunks = [...phase1Chunks, ...phase2Chunks];

    // Two-phase translation progress tracking
    const phase1ChunkCount = phase1Chunks.length;
    const phase2ChunkCount = phase2Chunks.length;
    const totalChunks = chunks.length;
    let phase1Complete = false;

    // Ultra-high parallelization for maximum speed
    const concurrencyLimit = 100; // Maximum concurrent chunks for ultimate speed
    const chunkQueue = [...chunks];
    const activePromises = new Map();
    const results = [];

    // Enhanced progress function for two-phase translation
    const updateProgress = () => {
      const completedChunks = totalChunks - chunkQueue.length;
      const overallProgress = Math.min((completedChunks / totalChunks) * 100, 100).toFixed(0);

      // Check if Phase 1 is complete
      if (!phase1Complete && completedChunks >= phase1ChunkCount) {
        phase1Complete = true;
        // Show Phase 1 completion message
        if (targetLanguage === "fa") {
          progressIndicator.textContent = `محتوای قابل مشاهده ترجمه شد! ادامه ترجمه در پس‌زمینه... ${overallProgress}٪`;
        } else {
          progressIndicator.textContent = `Visible content translated! Continuing in background... ${overallProgress}%`;
        }

        // Make progress indicator less prominent after Phase 1
        progressIndicator.style.opacity = "0.7";
        progressIndicator.style.fontSize = "12px";
        progressIndicator.style.top = "auto";
        progressIndicator.style.bottom = "20px";
        progressIndicator.style.right = "20px";
        progressIndicator.style.left = "auto";
        progressIndicator.style.transform = "none";
        progressIndicator.style.padding = "8px 12px";
        progressIndicator.style.borderRadius = "20px";
      } else if (!phase1Complete) {
        // Phase 1 in progress - prominent display
        const phase1Progress = Math.min((completedChunks / phase1ChunkCount) * 100, 100).toFixed(0);
        if (targetLanguage === "fa") {
          progressIndicator.textContent = `ترجمه محتوای قابل مشاهده... ${phase1Progress}٪`;
        } else {
          progressIndicator.textContent = `Translating visible content... ${phase1Progress}%`;
        }
      } else {
        // Phase 2 in progress - background translation
        if (targetLanguage === "fa") {
          progressIndicator.textContent = `ترجمه کامل صفحه... ${overallProgress}٪`;
        } else {
          progressIndicator.textContent = `Complete page translation... ${overallProgress}%`;
        }
      }
    };

    // Two-phase processing with background continuation
    let currentChunkIndex = 0;

    // Process chunks with controlled concurrency and phase awareness
    while (chunkQueue.length > 0 || activePromises.size > 0) {
      // Adjust concurrency based on phase
      const currentConcurrency = phase1Complete ?
        Math.min(concurrencyLimit, 50) : // Reduce concurrency in Phase 2 to avoid blocking
        concurrencyLimit; // Full concurrency for Phase 1

      while (chunkQueue.length > 0 && activePromises.size < currentConcurrency) {
        const chunk = chunkQueue.shift();
        const chunkId = Math.random().toString(36).substring(2, 9);
        const isPhase1Chunk = currentChunkIndex < phase1ChunkCount;

        const chunkPromise = processChunk(
          chunk,
          targetLanguage,
          apiKey,
          model,
          tone,
          isPhase1Chunk
        ).then((result) => {
          activePromises.delete(chunkId);
          updateProgress();

          // If this was the last Phase 1 chunk, trigger Phase 1 completion
          if (isPhase1Chunk && currentChunkIndex === phase1ChunkCount - 1) {
            // Allow a brief moment for UI to update before continuing
            setTimeout(() => {
              console.log("Phase 1 (visible content) translation completed. Starting Phase 2 (background).");
            }, 100);
          }

          return result;
        });

        activePromises.set(chunkId, chunkPromise);
        results.push(chunkPromise);
        currentChunkIndex++;

        // Add small delay between Phase 2 chunks to prevent blocking
        if (phase1Complete && chunkQueue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      if (activePromises.size > 0) {
        await Promise.race(Array.from(activePromises.values()));
      }
      updateProgress();
    }
    await Promise.all(results);

    // Show completion notification for two-phase translation
    if (phase1Complete && phase2ChunkCount > 0) {
      // Show final completion message
      if (targetLanguage === "fa") {
        progressIndicator.textContent = "ترجمه کامل صفحه تکمیل شد!";
      } else {
        progressIndicator.textContent = "Complete page translation finished!";
      }

      // Auto-hide after 3 seconds
      setTimeout(() => {
        if (document.getElementById("translation-progress")) {
          document.getElementById("translation-progress").remove();
        }
      }, 3000);
    } else {
      // Remove progress indicator immediately for single-phase translations
      if (document.getElementById("translation-progress")) {
        document.getElementById("translation-progress").remove();
      }
    }

    // Add a button to restore the original content
    const restoreButton = document.createElement("button");
    restoreButton.textContent =
      targetLanguage === "fa" ? "بازگرداندن متن اصلی" : "Restore Original";
    restoreButton.style.position = "fixed";
    restoreButton.style.lineHeight = "10px";
    restoreButton.style.top = "10px";
    restoreButton.style.right = "10px";
    restoreButton.style.zIndex = "9999";
    restoreButton.style.padding = "10px";
    restoreButton.style.backgroundColor = "#1a73e8";
    restoreButton.style.color = "white";
    restoreButton.style.border = "none";
    restoreButton.style.borderRadius = "4px";
    restoreButton.style.cursor = "pointer";

    // Add RTL support for Persian
    if (targetLanguage === "fa") {
      restoreButton.style.fontFamily = "Tahoma, Arial, sans-serif";
      restoreButton.dir = "rtl";
    }

    // Use a more reliable click handler with a flag to prevent multiple clicks
    let isRestoring = false;

    restoreButton.addEventListener("click", () => {
      // Prevent multiple clicks from causing issues
      if (isRestoring) {
        console.log("Restoration already in progress, please wait...");
        return;
      }

      isRestoring = true;
      console.log("Starting page restoration process...");

      // Restore original title
      browser.storage.local
        .get([
          "originalTextNodes",
          "originalTitle",
          "persistentTranslationEnabled",
          "domainTranslationSettings",
        ])
        .then((result) => {
          // If persistent translation is enabled, ask the user if they want to disable it
          if (result.persistentTranslationEnabled) {
            const disablePersistent = confirm(
              targetLanguage === "fa"
                ? "آیا می‌خواهید ترجمه خودکار را برای صفحات بعدی نیز غیرفعال کنید؟"
                : "Do you want to disable automatic translation for future pages as well?"
            );

            if (disablePersistent) {
              // Disable persistent translation globally
              browser.storage.local.set({
                persistentTranslationEnabled: false,
              });

              // Remove this domain from the domain settings
              if (result.domainTranslationSettings) {
                try {
                  const domainSettings = JSON.parse(
                    result.domainTranslationSettings
                  );
                  if (domainSettings[currentDomain]) {
                    delete domainSettings[currentDomain];
                    browser.storage.local.set({
                      domainTranslationSettings: JSON.stringify(domainSettings),
                    });
                    console.log(
                      "Removed translation settings for domain:",
                      currentDomain
                    );
                  }
                } catch (error) {
                  console.error(
                    "Error removing domain translation settings:",
                    error
                  );
                }
              }

              browser.runtime
                .sendMessage({
                  action: "setPersistentTranslation",
                  enabled: false,
                  domain: currentDomain,
                })
                .catch((error) => {
                  console.error(
                    "Error disabling persistent translation:",
                    error
                  );
                });
            }
          }

          // Reset the page translated flag
          pageTranslated = false;

          if (result.originalTitle) {
            document.title = result.originalTitle;
          }

          if (result.originalTextNodes) {
            const originalTexts = JSON.parse(result.originalTextNodes);

            // Remove RTL support
            removeRTLSupport();

            // Restore original text nodes
            const walker = document.createTreeWalker(
              document.body,
              NodeFilter.SHOW_TEXT,
              {
                acceptNode: function (node) {
                  if (node.nodeValue.trim() === "") {
                    return NodeFilter.FILTER_REJECT;
                  }
                  if (
                    node.parentNode.tagName === "SCRIPT" ||
                    node.parentNode.tagName === "STYLE" ||
                    node.parentNode.tagName === "NOSCRIPT"
                  ) {
                    return NodeFilter.FILTER_REJECT;
                  }
                  return NodeFilter.FILTER_ACCEPT;
                },
              }
            );

            let node;
            let i = 0;
            while ((node = walker.nextNode())) {
              if (i < originalTexts.length) {
                node.nodeValue = originalTexts[i];
                i++;
              }
            }

            // Remove any spans we created for RTL support
            const translatedSpans = document.querySelectorAll(
              'span[data-gemini-translated="true"]'
            );
            translatedSpans.forEach((span) => {
              // If we have the original text for this node, replace it
              if (i < originalTexts.length) {
                const textNode = document.createTextNode(originalTexts[i]);
                span.parentNode.replaceChild(textNode, span);
                i++;
              } else {
                // Otherwise just remove the span and keep its contents
                const textContent = span.textContent;
                const textNode = document.createTextNode(textContent);
                span.parentNode.replaceChild(textNode, span);
              }
            });

            // Remove the restore button
            restoreButton.remove();

            console.log("Page restored to original content");
          }

          // Reset the restoration flag
          isRestoring = false;
        })
        .catch((error) => {
          console.error("Error during page restoration:", error);
          // Reset the restoration flag even if there's an error
          isRestoring = false;
        });
    });

    document.body.appendChild(restoreButton);

    return true;
  } catch (error) {
    console.error("Translation error:", error);

    // Remove progress indicator if it exists
    const progressIndicator = document.getElementById("translation-progress");
    if (progressIndicator) {
      progressIndicator.remove();
    }

    throw error;
  }
}

// Helper function to process a chunk of text nodes with phase-aware optimization
async function processChunk(
  chunk,
  targetLanguage,
  apiKey,
  model,
  tone = "neutral",
  isPhase1 = false
) {
  try {
    // Ultra-fast processing optimizations for Phase 1 (visible content)
    if (isPhase1) {
      // Force fastest model for immediate visibility
      model = "gemini-1.5-flash";
    }
    // Create a JSON structure for translation to maintain exact mapping
    const textsToTranslate = chunk.map((item, index) => ({
      id: index + 1,
      text: item.text,
      isEnglish: isEnglishText(item.text), // Use our improved English detection function
    }));

    // Filter out English texts if target language is English to avoid unnecessary translations
    // Also check cache for existing translations
    const textsNeedingTranslation = [];
    const cachedTranslations = [];

    // Process all texts in parallel for cache checking
    const cacheCheckPromises = textsToTranslate.map(async (item) => {
      // Skip English texts if target language is English
      if (targetLanguage === "en" && item.isEnglish) {
        return { id: item.id, cached: true, skip: true };
      }

      // Check cache
      const cachedTranslation = await getCachedTranslation(
        item.text,
        targetLanguage,
        model
      );
      if (cachedTranslation) {
        return { id: item.id, cached: true, text: cachedTranslation };
      } else {
        return { id: item.id, cached: false, item };
      }
    });

    // Wait for all cache checks to complete
    const cacheResults = await Promise.all(cacheCheckPromises);

    // Process cache results
    for (const result of cacheResults) {
      if (result.skip) continue;

      if (result.cached) {
        cachedTranslations.push({
          id: result.id,
          text: result.text,
        });
      } else {
        textsNeedingTranslation.push(
          textsToTranslate.find((item) => item.id === result.id)
        );
      }
    }

    // Apply cached translations immediately
    for (const item of cachedTranslations) {
      const originalIndex = item.id - 1;
      if (originalIndex >= 0 && originalIndex < chunk.length) {
        chunk[originalIndex].node.nodeValue = cleanTranslatedText(
          item.text,
          targetLanguage
        );
      }
    }

    // If no texts need translation, we're done with this chunk
    if (textsNeedingTranslation.length === 0) {
      return;
    }

    // Create an ultra-compact prompt for maximum speed
    let prompt = `You are a professional translator. Translate to ${getLanguageName(
      targetLanguage
    )}. IMPORTANT: Provide ONLY the translated text without any explanations. Do not transliterate - translate the meaning.`;

    // Add tone instructions if a tone is selected
    if (tone && tone !== "neutral") {
      const toneInstructions = {
        casual: "Use a casual, friendly, and conversational tone.",
        formal: "Use a formal, professional, and polite tone.",
        simple: "Use simple, clear language that's easy to understand.",
        literary: "Use a literary, poetic, and expressive tone.",
      };

      if (toneInstructions[tone]) {
        prompt += `. ${toneInstructions[tone]}`;
      }
    }

    prompt += `. Return only JSON array.
    ${JSON.stringify(textsNeedingTranslation)}`;

    // Use ultra-fast model for Phase 1, fast model for Phase 2
    const fastModel = isPhase1 ? "gemini-1.5-flash" : "gemini-1.5-flash";

    // Get translations with optimized model selection for speed
    const translatedTextsJson = await callGeminiAPI(prompt, apiKey, fastModel);

    // Try to parse the JSON response
    let translatedItems = [];

    // Extract JSON from the response (in case the API returns additional text)
    const jsonMatch =
      translatedTextsJson.match(/\[\s*\{.*\}\s*\]/s) ||
      translatedTextsJson.match(/\[\s*\{[\s\S]*\}\s*\]/g);

    if (jsonMatch) {
      translatedItems = JSON.parse(jsonMatch[0]);
    } else {
      // Try to parse the entire response as JSON
      try {
        translatedItems = JSON.parse(translatedTextsJson);
      } catch (e) {
        console.error("Failed to parse JSON response:", e);
        // Fallback to regex-based parsing
        const lines = translatedTextsJson.split("\n");
        for (let j = 0; j < textsNeedingTranslation.length; j++) {
          const id = textsNeedingTranslation[j].id;
          const lineRegex = new RegExp(
            `"id"\\s*:\\s*${id}\\s*,\\s*"text"\\s*:\\s*"([^"]*)"`
          );
          const matchingLine = lines.find((line) => lineRegex.test(line));

          if (matchingLine) {
            const match = matchingLine.match(lineRegex);
            if (match && match[1]) {
              translatedItems.push({
                id: id,
                text: match[1].replace(/\\"/g, '"'),
              });
            }
          }
        }
      }
    }

    // Update the text nodes with translations and cache them
    for (const translatedItem of translatedItems) {
      const originalIndex = translatedItem.id - 1;
      if (originalIndex >= 0 && originalIndex < chunk.length) {
        // Clean up the translated text before applying it
        const cleanedText = cleanTranslatedText(
          translatedItem.text,
          targetLanguage
        );

        // Get the parent element for RTL support
        const parentElement = chunk[originalIndex].parentElement;

        // Add RTL support for Persian/Arabic text
        if (targetLanguage === "fa" || targetLanguage === "ar") {
          // Mark the parent element as translated for RTL styling
          if (parentElement) {
            parentElement.setAttribute("data-gemini-translated", "true");

            // Handle mixed content (English words in Persian text)
            if (cleanedText.match(/[a-zA-Z0-9]+/)) {
              // If parent is not a text-only element, we can use a safer DOM approach
              if (parentElement.children.length > 0) {
                // Create a temporary span to hold the text
                const tempSpan = document.createElement("span");
                tempSpan.setAttribute("data-gemini-translated", "true");

                // Split the text by English words and create appropriate nodes
                const parts = cleanedText.split(/([a-zA-Z0-9]+)/);

                for (let i = 0; i < parts.length; i++) {
                  if (parts[i].match(/^[a-zA-Z0-9]+$/)) {
                    // This is an English word, create a span with LTR class
                    const ltrSpan = document.createElement("span");
                    ltrSpan.className = "gemini-ltr";
                    ltrSpan.textContent = parts[i];
                    tempSpan.appendChild(ltrSpan);
                  } else if (parts[i]) {
                    // This is non-English text, add as a text node
                    tempSpan.appendChild(document.createTextNode(parts[i]));
                  }
                }

                // Replace the text node with our new span
                chunk[originalIndex].node.parentNode.replaceChild(
                  tempSpan,
                  chunk[originalIndex].node
                );
                continue; // Skip the normal text node update
              }
            }
          }
        }

        // Normal update for non-RTL or simple text
        chunk[originalIndex].node.nodeValue = cleanedText;

        // Cache the translation
        const originalText = chunk[originalIndex].text;
        cacheTranslation(
          originalText,
          translatedItem.text,
          targetLanguage,
          model
        );
      }
    }

    // For English texts when target is English, keep them as is
    if (targetLanguage === "en") {
      for (const item of textsToTranslate) {
        if (item.isEnglish) {
          // Keep English text as is
          const originalIndex = item.id - 1;
          if (originalIndex >= 0 && originalIndex < chunk.length) {
            chunk[originalIndex].node.nodeValue = item.text;
          }
        }
      }
    }
  } catch (error) {
    console.error("Error processing chunk:", error);
    // We don't rethrow the error to allow other chunks to continue processing
  }
}

// Function to ask Gemini a question about the page or any general topic
async function askGemini(
  pageContent,
  question,
  apiKey,
  model = "gemini-pro",
  responseLang = "en",
  tone = "neutral"
) {
  // Extract the most relevant content from the page
  const contentExcerpt = pageContent.content.substring(0, 10000); // Increased limit for better context

  // Get language name for better instructions
  const languageName = getLanguageName(responseLang);

  // Create a more detailed prompt for Gemini to answer the question
  let prompt = `You are an AI assistant helping a user with their questions. You can answer questions about the current web page or any general topic.

  Web Page Information:
  Title: ${pageContent.title}
  Description: ${pageContent.metaDescription}

  Page Content (excerpt):
  ${contentExcerpt}

  User Question: ${question}

  Instructions:
  1. First, determine if the question is about the web page content or a general topic.
  2. If the question appears to be about the web page, answer based on the information provided in the web page content above.
  3. If the question is about a general topic not related to the web page, provide a helpful answer based on your knowledge.
  4. When answering general questions, use your full capabilities and knowledge to provide accurate information.
  5. Format your answer with proper paragraphs, bullet points, or numbered lists when appropriate.
  6. If the question is about code or technical content, format code examples properly.
  7. Provide a comprehensive but concise answer.
  8. IMPORTANT: Your response must be in ${languageName} language.`;

  // Add tone instructions based on the selected tone
  if (tone && tone !== "neutral") {
    const toneInstructions = {
      casual:
        "9. Use a casual, friendly, and conversational tone in your response. Be approachable and use informal language.",
      formal:
        "9. Use a formal, professional, and polite tone in your response. Maintain proper etiquette and use precise language.",
      simple:
        "9. Use simple, clear language that's easy to understand. Avoid complex terminology and explain concepts in straightforward terms.",
      literary:
        "9. Use a literary, poetic, and expressive tone in your response. Employ rich vocabulary and elegant phrasing.",
    };

    if (toneInstructions[tone]) {
      prompt += `\n  ${toneInstructions[tone]}`;
    }
  }

  prompt += `\n\n  Your answer:`;

  try {
    const answer = await callGeminiAPI(prompt, apiKey, model);
    return answer;
  } catch (error) {
    console.error("Question answering error:", error);
    throw error;
  }
}

// Cache for model name mapping to avoid repeated lookups
const modelNameCache = new Map();

// Request deduplication cache to avoid duplicate API calls
const activeRequests = new Map();

// Function to call the Gemini API with maximum performance optimization
async function callGeminiAPI(prompt, apiKey, model = "gemini-pro") {
  // Create request key for deduplication
  const requestKey = `${prompt.substring(0, 100)}|${model}|${apiKey.substring(0, 10)}`;

  // Check if the same request is already in progress
  if (activeRequests.has(requestKey)) {
    return activeRequests.get(requestKey);
  }
  // Get model name from cache or map it
  let modelName = modelNameCache.get(model);

  if (!modelName) {
    // Map friendly names to API model names if needed
    const modelMap = {
      "Gemini 1.5 Flash": "gemini-1.5-flash",
      "Gemini Pro": "gemini-pro",
      "Gemini 1.0 Pro": "gemini-1.0-pro",
      "Gemini Pro Vision": "gemini-pro-vision",
      "Gemini 2.0 Flash": "gemini-2.0-flash", // Use actual model name
      "Gemini 2.5 Flash Preview 04-17": "gemini-2.5-flash-preview-04-17", // Use actual model name with date
      "Gemini 2.5 Pro Preview 03-25": "gemini-2.5-pro-preview-03-25", // Use actual model name with date
    };

    modelName = modelMap[model] || model;
    // Cache the model name mapping for future use
    modelNameCache.set(model, modelName);
  }

  const apiUrl = `https://generativelanguage.googleapis.com/v1/models/${modelName}:generateContent`;

  // Ultra-optimized request data for maximum speed
  const requestData = {
    contents: [{ parts: [{ text: prompt }] }],
    generationConfig: {
      temperature: 0.0, // Minimum temperature for fastest, most deterministic responses
      maxOutputTokens: 1024, // Further reduced token limit for faster processing
      topK: 1, // Limit token selection for fastest generation
      topP: 0.7, // More focused sampling for even faster generation
      candidateCount: 1, // Only generate one candidate for speed
    },
  };

  // Create the promise and cache it
  const requestPromise = (async () => {
    try {
      // Make the API request with optimized settings and timeout
      const response = await fetch(`${apiUrl}?key=${apiKey}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Priority": "high", // Request high priority processing
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error?.message || response.statusText;

        // Check for specific error types and provide more helpful messages
        if (errorMessage.includes("quota")) {
          throw new Error(
            `Your API quota has been exhausted. Please check your Google AI Studio account and verify your quota status or try a different model.`
          );
        } else if (errorMessage.includes("not found")) {
          throw new Error(
            `The selected model (${modelName}) is not available for your API key. Please try a different model.`
          );
        } else if (errorMessage.includes("invalid")) {
          throw new Error(
            `Invalid API key. Please check your API key or create a new one.`
          );
        } else {
          throw new Error(`API Error: ${errorMessage}`);
        }
      }

      const data = await response.json();

      // Extract the text from the response
      if (
        data.candidates &&
        data.candidates[0] &&
        data.candidates[0].content &&
        data.candidates[0].content.parts
      ) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error(
          "Unexpected API response format. Please try again or try a different model."
        );
      }
    } catch (error) {
      console.error("Gemini API error:", error);
      throw error;
    } finally {
      // Remove from active requests when done
      activeRequests.delete(requestKey);
    }
  })();

  // Cache the promise
  activeRequests.set(requestKey, requestPromise);

  return requestPromise;
}

// Helper function to get language name from code
function getLanguageName(langCode) {
  const languages = {
    en: "English",
    fa: "Persian (Farsi)",
    fr: "French",
    de: "German",
    es: "Spanish",
    ar: "Arabic",
    zh: "Chinese",
    ru: "Russian",
  };

  return languages[langCode] || langCode;
}

// Helper function to detect if text is primarily English
function isEnglishText(text) {
  if (!text || text.trim() === "") return true;

  // Count English characters vs. non-English characters
  const englishChars = text.match(/[a-zA-Z0-9\s\.,\?!;:'"\(\)\-]/g) || [];

  // If more than 70% of characters are English, consider it English text
  return englishChars.length > text.length * 0.7;
}

// Helper function to clean up translated text and fix formatting issues
function cleanTranslatedText(text, targetLanguage) {
  if (!text) return "";

  // Remove extra quotes that might be added by the API
  let cleaned = text.replace(/^["']|["']$/g, "");

  // Fix common formatting issues
  cleaned = cleaned.replace(/\\n/g, "\n");
  cleaned = cleaned.replace(/\\"/g, '"');
  cleaned = cleaned.replace(/\\'/g, "'");

  // Fix spacing issues that often occur in translations
  if (targetLanguage === "fa" || targetLanguage === "ar") {
    // For RTL languages, fix common spacing issues
    cleaned = cleaned.replace(/ ([،؛؟!])/g, "$1"); // Remove space before punctuation
    cleaned = cleaned.replace(/([،؛؟!])([^\s])/g, "$1 $2"); // Add space after punctuation if needed
  } else {
    // For LTR languages
    cleaned = cleaned.replace(/([.,;?!])([^\s])/g, "$1 $2"); // Add space after punctuation if needed
  }

  return cleaned;
}

// --- YouTube Subtitles Functionality ---
// Handle YouTube subtitle-related messages
function handleYouTubeSubtitleMessage(message, sendResponse) {
  console.log("Handling YouTube subtitle message:", message.action);

  if (message.action === "loood") {
    console.log("Processing subtitle file upload:", message.fileName);
    const { fileContent, fileName, targetLanguage, tone } = message;

    try {
      // Process the subtitle file
      processSubtitleFile(fileContent, fileName, targetLanguage, tone)
        .then((result) => {
          sendResponse({
            success: true,
            message: "Subtitles processed successfully!",
            data: result
          });
        })
        .catch((error) => {
          console.error("Error processing subtitles:", error);
          sendResponse({
            success: false,
            message: "Failed to process subtitles: " + error.message
          });
        });
    } catch (error) {
      console.error("Error in subtitle processing:", error);
      sendResponse({
        success: false,
        message: "Failed to process subtitles: " + error.message
      });
    }
    return true; // Keep message channel open for async response
  }

  if (message.action === "testSubtitleDisplay") {
    console.log("Testing subtitle display");
    try {
      showTestSubtitle();
      sendResponse({
        success: true,
        message: "Test subtitle displayed successfully"
      });
    } catch (error) {
      console.error("Error showing test subtitle:", error);
      sendResponse({
        success: false,
        message: "Failed to show test subtitle: " + error.message
      });
    }
    return true;
  }

  if (message.action === "updateYouTubeSubtitlesSettings") {
    console.log("Updating YouTube subtitle settings:", message);
    try {
      updateSubtitleSettings(message);
      sendResponse({
        success: true,
        message: "Settings updated successfully"
      });
    } catch (error) {
      console.error("Error updating settings:", error);
      sendResponse({
        success: false,
        message: "Failed to update settings: " + error.message
      });
    }
    return true;
  }

  if (message.action === "updateSubtitleCustomization") {
    console.log("Updating subtitle customization:", message);
    try {
      updateSubtitleCustomization(message);
      sendResponse({
        success: true,
        message: "Customization updated successfully"
      });
    } catch (error) {
      console.error("Error updating customization:", error);
      sendResponse({
        success: false,
        message: "Failed to update customization: " + error.message
      });
    }
    return true;
  }

  return false; // Message not handled
}

// Process subtitle file content
async function processSubtitleFile(fileContent, fileName, targetLanguage, tone) {
  console.log("Processing subtitle file:", fileName);

  // Parse SRT content
  const subtitles = parseSRTContent(fileContent);
  console.log("Parsed subtitles:", subtitles.length, "entries");

  // Translate subtitles
  const translatedSubtitles = await translateSubtitles(subtitles, targetLanguage, tone);

  // Store translated subtitles for display
  storeTranslatedSubtitles(translatedSubtitles);

  return {
    originalCount: subtitles.length,
    translatedCount: translatedSubtitles.length,
    fileName: fileName
  };
}

// Parse SRT file content
function parseSRTContent(content) {
  const subtitles = [];
  const blocks = content.trim().split(/\n\s*\n/);

  blocks.forEach((block, index) => {
    const lines = block.trim().split('\n');
    if (lines.length >= 3) {
      const id = lines[0].trim();
      const timeRange = lines[1].trim();
      const text = lines.slice(2).join('\n').trim();

      // Parse time range (e.g., "00:00:01,000 --> 00:00:03,000")
      const timeMatch = timeRange.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
      if (timeMatch) {
        subtitles.push({
          id: parseInt(id) || index + 1,
          startTime: timeMatch[1],
          endTime: timeMatch[2],
          text: text,
          startSeconds: timeToSeconds(timeMatch[1]),
          endSeconds: timeToSeconds(timeMatch[2])
        });
      }
    }
  });

  return subtitles;
}

// Convert time string to seconds
function timeToSeconds(timeStr) {
  const [time, ms] = timeStr.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + parseInt(ms) / 1000;
}

// Translate subtitles using Gemini API
async function translateSubtitles(subtitles, targetLanguage, tone) {
  console.log("Translating", subtitles.length, "subtitles to", targetLanguage);

  // Get API settings
  const settings = await browser.storage.local.get(['apiKey', 'model']);
  if (!settings.apiKey) {
    throw new Error("API key not found. Please configure your Gemini API key.");
  }

  const translatedSubtitles = [];

  // Process subtitles in batches to avoid API limits
  const batchSize = 5;
  for (let i = 0; i < subtitles.length; i += batchSize) {
    const batch = subtitles.slice(i, i + batchSize);
    const batchTexts = batch.map(sub => sub.text);

    try {
      const translatedTexts = await translateTextBatch(batchTexts, targetLanguage, tone, settings.apiKey, settings.model);

      batch.forEach((subtitle, index) => {
        translatedSubtitles.push({
          ...subtitle,
          originalText: subtitle.text,
          text: translatedTexts[index] || subtitle.text
        });
      });

      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.error("Error translating batch:", error);
      // Add original text if translation fails
      batch.forEach(subtitle => {
        translatedSubtitles.push({
          ...subtitle,
          originalText: subtitle.text
        });
      });
    }
  }

  return translatedSubtitles;
}

// Translate a batch of texts
async function translateTextBatch(texts, targetLanguage, tone, apiKey, model) {
  const prompt = `Translate the following subtitle texts to ${targetLanguage} with ${tone} tone. Keep the same number of lines and preserve timing-appropriate length. Return only the translations, one per line, in the same order:

${texts.map((text, i) => `${i + 1}. ${text}`).join('\n')}`;

  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: 0.3,
        maxOutputTokens: 2048,
      }
    })
  });

  if (!response.ok) {
    throw new Error(`Translation API error: ${response.status}`);
  }

  const data = await response.json();
  const translatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

  if (!translatedText) {
    throw new Error("No translation received from API");
  }

  // Parse the response to extract individual translations
  const lines = translatedText.trim().split('\n');
  return texts.map((_, index) => {
    const line = lines.find(l => l.startsWith(`${index + 1}.`));
    return line ? line.substring(line.indexOf('.') + 1).trim() : texts[index];
  });
}

// Store translated subtitles for display
function storeTranslatedSubtitles(subtitles) {
  window.translatedSubtitles = subtitles;
  console.log("Stored", subtitles.length, "translated subtitles");
}

// Show test subtitle
function showTestSubtitle() {
  const testSubtitle = {
    text: "This is a test subtitle - زیرنویس آزمایشی",
    startTime: "00:00:00,000",
    endTime: "00:00:05,000"
  };

  displaySubtitle(testSubtitle);

  // Hide after 3 seconds
  setTimeout(() => {
    hideSubtitle();
  }, 3000);
}

// Display subtitle on page
function displaySubtitle(subtitle) {
  // Remove existing subtitle
  hideSubtitle();

  // Create subtitle element
  const subtitleElement = document.createElement('div');
  subtitleElement.id = 'gemini-subtitle-display';
  subtitleElement.textContent = subtitle.text;

  // Apply styling
  Object.assign(subtitleElement.style, {
    position: 'fixed',
    bottom: '100px',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    color: 'white',
    padding: '8px 16px',
    borderRadius: '4px',
    fontSize: '16px',
    fontFamily: 'Arial, sans-serif',
    zIndex: '10000',
    maxWidth: '80%',
    textAlign: 'center',
    lineHeight: '1.4',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
  });

  document.body.appendChild(subtitleElement);
}

// Hide subtitle
function hideSubtitle() {
  const existing = document.getElementById('gemini-subtitle-display');
  if (existing) {
    existing.remove();
  }
}

// Update subtitle settings
function updateSubtitleSettings(settings) {
  window.youtubeSubtitleSettings = {
    enabled: settings.enabled,
    targetLanguage: settings.targetLanguage,
    tone: settings.tone
  };
  console.log("Updated subtitle settings:", window.youtubeSubtitleSettings);
}

// Update subtitle customization
function updateSubtitleCustomization(customization) {
  window.subtitleCustomization = {
    fontSize: customization.fontSize,
    bgColor: customization.bgColor,
    textColor: customization.textColor,
    opacity: customization.opacity
  };
  console.log("Updated subtitle customization:", window.subtitleCustomization);
}
