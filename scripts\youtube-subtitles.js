try {
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("Processing subtitles:");
    if (message.action == "loood") {
        
      console.log("Processing subtitlesfffffffffffffffffffffffffffffffff:");
      const { fileContent, fileName, targetLanguage, tone } = message;

      // انجام عملیات مورد نظر...

      // ارسال پاسخ
      sendResponse({
        success: true,
        message: "Subtitles processed successfully!",
      });
    }

    return true;
  });
} catch {
    console.log('object')
}
