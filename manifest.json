{"manifest_version": 2, "name": "Gemini Web Translator", "version": "2.1", "description": "Translate web pages and ask questions using Gemini API", "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "64": "icons/icon-64.png", "96": "icons/icon-96.png", "128": "icons/icon-128.png"}, "browser_specific_settings": {"gecko": {"id": "<EMAIL>", "strict_min_version": "102.0"}}, "permissions": ["activeTab", "storage", "<all_urls>", "tabs", "downloads", "scripting"], "web_accessible_resources": ["styles/selection-translator.css", "popup/popup.html", "styles/popup.css", "scripts/popup.js"], "browser_action": {"default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "64": "icons/icon-64.png"}, "default_title": "Gemini Translator"}, "page_action": {"default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "64": "icons/icon-64.png"}, "default_title": "Translate this page with Gemini", "default_popup": "popup/popup.html"}, "background": {"scripts": ["background/background.js"]}, "options_ui": {"page": "getting-started.html", "open_in_tab": true}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["scripts/content.js", "scripts/keyboard-shortcuts.js", "scripts/selected-text-translation.js", "scripts/mobile-popup.js", "scripts/youtube-subtitles.js"], "run_at": "document_end", "all_frames": false}]}